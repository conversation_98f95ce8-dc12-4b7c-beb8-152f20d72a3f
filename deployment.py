import os
import json
import base64
from typing import Any, Dict

from langchain.prompts import ChatPromptTemplate
from langchain.schema.messages import HumanMessage, SystemMessage
from langchain_core.output_parsers import StrOutputParser
from langchain_openai import AzureChatOpenAI
from langgraph.graph import StateGraph, END
from langgraph.prebuilt import create_react_agent

import config
from data.State import DeploymentState
from tool.screen_content import *

os.environ["LANGCHAIN_TRACING_V2"] = config.LANGCHAIN_TRACING_V2
os.environ["LANGCHAIN_ENDPOINT"] = config.LANGCHAIN_ENDPOINT
os.environ["LANGCHAIN_API_KEY"] = config.LANGCHAIN_API_KEY
os.environ["LANGCHAIN_PROJECT"] = "DeploymentExecution"

model = AzureChatOpenAI(
    azure_endpoint=config.LLM_BASE_URL,
    openai_api_key=config.LLM_API_KEY,
    deployment_name=config.LLM_MODEL,
    request_timeout=config.LLM_REQUEST_TIMEOUT,
    openai_api_version="2024-02-15-preview",
    max_retries=config.LLM_MAX_RETRIES,
    max_tokens=config.LLM_MAX_TOKEN,
)


def create_execution_state(device: str) -> Dict[str, Any]:
    """
    Create initial execution state

    Args:
        device: Device ID

    Returns:
        Dictionary containing initial state
    """
    from data.State import create_deployment_state

    state = create_deployment_state(
        task="",
        device=device,
    )

    return state


def capture_and_parse_screen(state: DeploymentState) -> DeploymentState:
    """
    Capture current screen and parse elements, update state

    Args:
        state: Deployment state

    Returns:
        Updated deployment state
    """
    try:
        # 1. Take screenshot
        screenshot_path = take_screenshot.invoke(
            {
                "device": state["device"],
                "app_name": "deployment",
                "step": state["current_step"],
            }
        )

        if not screenshot_path or not os.path.exists(screenshot_path):
            print("❌ Screenshot failed")
            return state

        # 2. Parse screen elements and generate labeled image
        screen_result = screen_element.invoke({"image_path": screenshot_path})

        if "error" in screen_result:
            print(f"❌ Screen element parsing failed: {screen_result['error']}")
            return state

        # 3. Update current page information
        state["current_page"]["screenshot"] = screenshot_path
        state["current_page"]["labeled_screenshot"] = screen_result.get("labeled_image_path")
        state["current_page"]["elements_json"] = screen_result[
            "parsed_content_json_path"
        ]

        # 4. Load element data
        with open(
                screen_result["parsed_content_json_path"], "r", encoding="utf-8"
        ) as f:
            state["current_page"]["elements_data"] = json.load(f)

        print(
            f"✓ Successfully parsed current screen, detected {len(state['current_page']['elements_data'])} UI elements"
        )
        print(f"✓ Generated labeled screenshot: {state['current_page']['labeled_screenshot']}")
        return state

    except Exception as e:
        print(f"❌ Error capturing and parsing screen: {str(e)}")
        return state


def get_location(state: DeploymentState) -> DeploymentState:
    """
    Get location coordinates through screenshot and model analysis, then execute operations

    Args:
        state: Execution state

    Returns:
        Updated execution state
    """
    print("🔄 Using get_location for task execution...")
    task = state["task"]

    # Create action_agent for page operation decisions
    action_agent = create_react_agent(model, [screen_action])

    # Initialize if no messages exist
    if not state["messages"]:
        # Set system prompt
        system_message = SystemMessage(
            content="""You are an intelligent smartphone operation assistant who will help users complete tasks on mobile devices.
You can help users by observing the screen and performing various operations (clicking, typing text, swiping, etc.).
Analyze the current screen content, determine the best next action, and use the appropriate tools to execute it.
Each step of the operation should move toward completing the user's goal task."""
        )

        state["messages"].append(system_message)

        # Add user task
        user_message = HumanMessage(
            content=f"I need to complete the following task on a mobile device: {task}"
        )
        state["messages"].append(user_message)

    # Capture current screen
    state = capture_and_parse_screen(state)
    if not state["current_page"]["screenshot"]:
        state["execution_status"] = "error"
        print("Unable to capture or parse screen")
        return state

    # Prepare screen information - use labeled image for better automation accuracy
    screenshot_path = state["current_page"]["screenshot"]
    labeled_screenshot_path = state["current_page"]["labeled_screenshot"]
    elements_json_path = state["current_page"]["elements_json"]
    device = state["device"]
    device_size = get_device_size.invoke(device)

    # Use labeled image if available, otherwise fall back to regular screenshot
    image_path_to_use = labeled_screenshot_path if labeled_screenshot_path and os.path.exists(
        labeled_screenshot_path) else screenshot_path

    # Load image as base64
    with open(image_path_to_use, "rb") as f:
        image_data = f.read()
        image_data_base64 = base64.b64encode(image_data).decode("utf-8")

    # Load element JSON data
    with open(elements_json_path, "r", encoding="utf-8") as f:
        elements_data = json.load(f)
        for element in elements_data:
            center_x = int((element['bbox'][0] + element['bbox'][2]) / 2 * device_size["width"])
            center_y = int((element['bbox'][1] + element['bbox'][3]) / 2 * device_size["height"])
            element['x'] = center_x
            element['y'] = center_y
            del element['bbox']
            del element['content']
            del element['type']

    elements_text = json.dumps(elements_data, ensure_ascii=False, indent=2)

    # Build messages for model analysis
    messages = [
        SystemMessage(
            content=f"""你是一个智能手机操作助手，你的目标是帮助用户完成智能手机上的任务. 请根据用户意图，使用对应的工具操作对应的元素."""
        ),
        HumanMessage(
            content=f"手机设备: {device}. 用户当前意图: {task}"
        ),
        HumanMessage(
            content=[
                {"type": "text",
                 "text": f"下面是标记元素后的截图和元素对应的坐标，每一块元素都有唯一的编号，每个编号都有对应的坐标:" + elements_text},
                {
                    "type": "image_url",
                    "image_url": {"url": f"data:image/jpeg;base64,{image_data_base64}"},
                },
            ],
        ),
    ]

    # Add these messages to state
    state["messages"].extend(messages)

    # Call action_agent for decision making and action execution
    action_result = action_agent.invoke({"messages": state["messages"][-4:]})

    # Parse results
    final_messages = action_result.get("messages", [])

    print(final_messages)

    if final_messages:
        # Add AI reply to message history
        ai_message = final_messages[-1]
        state["messages"].append(ai_message)

        # Extract recommended action from final_message
        recommended_action = ai_message.content.strip()

        # Update execution status
        state["current_step"] += 1
        state["history"].append(
            {
                "step": state["current_step"],
                "screenshot": screenshot_path,
                "elements_json": elements_json_path,
                "action": "get_location",
                "recommended_action": recommended_action,
                "status": "success",
            }
        )

        state["execution_status"] = "success"
        print(f"✓ get_location execution successful: {recommended_action}")
    else:
        error_msg = "get_location execution failed: No messages returned"
        print(f"❌ {error_msg}")

        # Update execution status
        state["history"].append(
            {
                "step": state["current_step"],
                "screenshot": screenshot_path,
                "elements_json": elements_json_path,
                "action": "get_location",
                "status": "error",
                "error": error_msg,
            }
        )

        state["execution_status"] = "error"

    return state


def run_task(task: str, device: str = "emulator-5554") -> Dict[str, Any]:
    """
    Execute a single task

    Args:
        task: User task description
        device: Device ID

    Returns:
        Execution result
    """
    print(f"🚀 Starting task execution: {task}")

    try:
        # Initialize state using create_deployment_state function
        from data.State import create_deployment_state

        state = create_deployment_state(
            task=task,
            device=device,
            max_retries=3,
        )

        # Execute task using LangGraph workflow
        workflow = build_workflow()
        app = workflow.compile()
        result = app.invoke(state)

        # Display final screenshot if execution was successful
        if (
                result["execution_status"] == "success"
                and result["current_page"]["screenshot"]
        ):
            try:
                from PIL import Image

                img = Image.open(result["current_page"]["screenshot"])
                img.show()
            except Exception as e:
                print(f"Unable to display final screenshot: {str(e)}")

        return {
            "status": result["execution_status"],
            "message": "Task execution completed",
            "steps_completed": result["current_step"],
            "total_steps": result["total_steps"],
        }

    except Exception as e:
        print(f"❌ Error executing task: {str(e)}")
        return {
            "status": "error",
            "message": f"Error executing task: {str(e)}",
            "error": str(e),
        }


def capture_screen_node(state: DeploymentState) -> DeploymentState:
    print("📸 Capturing and parsing current screen...")

    state_dict = dict(state)
    updated_state = capture_and_parse_screen(state_dict)

    # Update state
    for key, value in updated_state.items():
        if key in state:
            state[key] = value

    if not state["current_page"]["screenshot"]:
        print("❌ Unable to capture screen, will proceed to React mode")
    else:
        print("✓ Screen captured successfully")

    return state


def fallback_node(state: DeploymentState) -> DeploymentState:
    """
    Use get_location for task execution
    """
    # Call get_location function
    state = get_location(state)

    # Mark task as completed
    state["completed"] = True

    return state


# Routing functions
def is_task_completed(state: DeploymentState) -> str:
    """
    Check if task is completed
    """
    if state["completed"]:
        return "end"
    return "continue"


# Build simplified workflow
def build_workflow() -> StateGraph:
    """
    Build simplified workflow state graph
    """
    workflow = StateGraph(DeploymentState)

    # Add only essential nodes
    workflow.add_node("capture_screen", capture_screen_node)
    workflow.add_node("fallback", fallback_node)
    workflow.add_node("check_completion", check_task_completion)

    # Define simplified edges
    workflow.set_entry_point("capture_screen")

    # Direct flow: capture_screen -> fallback -> check_completion
    workflow.add_edge("capture_screen", "fallback")
    workflow.add_edge("fallback", "check_completion")

    # Routing after task completion check
    workflow.add_conditional_edges(
        "check_completion",
        is_task_completed,
        {"end": END, "continue": "capture_screen"},
    )

    return workflow


def check_task_completion(state: DeploymentState) -> DeploymentState:
    """
    Determine if task is completed

    Args:
        state: Execution state

    Returns:
        Updated execution state with task completion status
    """
    # Skip judgment if too few steps
    if state["current_step"] < 2:
        return state

    print("🔍 Evaluating if task is completed...")

    # Get task description
    task = state["task"]

    # Step 1: Generate task completion criteria
    completion_prompt = ChatPromptTemplate.from_messages(
        [
            (
                "system",
                "You are an assistant that will help analyze task completion criteria. Please carefully read the following user task:",
            ),
            (
                "human",
                f"The user's task is: {task}\nPlease describe clear, checkable task completion criteria. For example: 'When certain elements or states appear on the page, it indicates the task is complete.'",
            ),
        ]
    )

    completion_chain = completion_prompt | model | StrOutputParser()
    completion_criteria = completion_chain.invoke({})

    # Collect recent screenshots
    recent_screenshots = []
    for step in state["history"][-3:]:
        if "screenshot" in step and step["screenshot"]:
            recent_screenshots.append(step["screenshot"])

    if not recent_screenshots:
        if state["current_page"]["screenshot"]:
            recent_screenshots.append(state["current_page"]["screenshot"])

    if not recent_screenshots:
        print("⚠️ No screenshots available, cannot determine if task is complete")
        return state

    # Build image messages
    image_messages = []
    for idx, img_path in enumerate(recent_screenshots, start=1):
        if os.path.exists(img_path):
            with open(img_path, "rb") as f:
                img_data = base64.b64encode(f.read()).decode("utf-8")
            image_messages.append(
                HumanMessage(
                    content=[
                        {"type": "text", "text": f"Here is data for screenshot {idx}:"},
                        {
                            "type": "image_url",
                            "image_url": {"url": f"data:image/jpeg;base64,{img_data}"},
                        },
                    ]
                )
            )

    # Step 2: Determine if task is complete
    judgement_prompt = ChatPromptTemplate.from_messages(
        [
            (
                "system",
                "You are a page assessment assistant that will determine if a task is complete based on completion criteria and current page screenshots. Please only respond with 'yes' or 'no'.",
            ),
            (
                "human",
                f"The completion criteria is: {completion_criteria}\n"
                f"Based on the following screenshots, determine if the task is complete. Note that if screenshots are identical, it may indicate the task cannot proceed, so respond with 'yes' to end the program.",
            ),
        ]
    )

    # Combine all messages
    all_messages = list(judgement_prompt.messages) + image_messages

    # Call LLM for judgment
    judgement_response = model.invoke(all_messages)
    judgement_answer = judgement_response.content.strip()

    # Update task completion status
    if "yes" in judgement_answer.lower() or "complete" in judgement_answer.lower():
        state["completed"] = True
        state["execution_status"] = "completed"
        print(f"✓ Task completed: {judgement_answer}")
    else:
        state["completed"] = False
        print(f"⚠️ Task not completed: {judgement_answer}")

    # Add to history
    state["history"].append(
        {
            "step": state["current_step"],
            "action": "task_completion_check",
            "completion_criteria": completion_criteria,
            "judgement": judgement_answer,
            "status": "success",
            "completed": state["completed"],
        }
    )

    return state
